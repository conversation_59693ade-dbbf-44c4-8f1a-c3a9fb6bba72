{"private": true, "dependencies": {"types-registry": "^0.1.723"}, "devDependencies": {"@types/accepts": "^1.3.7", "@types/assert": "^1.5.11", "@types/benchmark": "^2.1.5", "@types/bluebird": "^3.5.42", "@types/body-parser": "^1.19.5", "@types/bytes": "^3.1.5", "@types/chai": "^5.2.2", "@types/chai-as-promised": "^8.0.2", "@types/co": "^4.6.6", "@types/connect-pg-simple": "^7.0.3", "@types/connect-redis": "^0.0.23", "@types/content-disposition": "^0.5.8", "@types/content-type": "^1.1.8", "@types/cookie-parser": "^1.4.8", "@types/cookie-session": "^2.0.49", "@types/cookie-signature": "^1.1.2", "@types/cookiejar": "^2.1.5", "@types/core-util-is": "^1.0.1", "@types/cors": "^2.8.18", "@types/debug": "^4.1.12", "@types/deep-equal": "^1.0.4", "@types/depd": "^1.1.37", "@types/destroy": "^1.0.3", "@types/ee-first": "^1.1.3", "@types/ejs": "^3.1.5", "@types/encodeurl": "^1.0.2", "@types/escape-html": "^1.0.4", "@types/eslint-plugin-markdown": "^2.0.2", "@types/etag": "^1.8.3", "@types/express": "^5.0.2", "@types/express-session": "^1.18.1", "@types/finalhandler": "^1.2.3", "@types/forwarded": "^0.1.3", "@types/fresh": "^0.5.2", "@types/hbs": "^4.0.4", "@types/http-errors": "^2.0.4", "@types/inherits": "^2.0.0", "@types/isarray": "^2.0.3", "@types/istanbul": "^0.4.34", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.17.17", "@types/lolex": "^5.1.6", "@types/media-typer": "^1.1.3", "@types/merge-descriptors": "^1.0.3", "@types/method-override": "^3.0.0", "@types/methods": "^1.1.4", "@types/mime-db": "^1.43.5", "@types/mime-types": "^3.0.0", "@types/mocha": "^10.0.10", "@types/morgan": "^1.9.9", "@types/ms": "^2.1.0", "@types/negotiator": "^0.6.3", "@types/node": "^22.15.30", "@types/node-fetch": "^2.6.12", "@types/object-assign": "^4.0.33", "@types/on-finished": "^2.3.4", "@types/on-headers": "^1.0.3", "@types/parseurl": "^1.3.3", "@types/passport": "^1.0.17", "@types/passport-local": "^1.0.38", "@types/passport-strategy": "^0.2.38", "@types/pause": "^0.1.3", "@types/pg": "^8.15.4", "@types/proxy-addr": "^2.0.3", "@types/proxyquire": "^1.3.31", "@types/qs": "^6.14.0", "@types/random-bytes": "^1.0.3", "@types/range-parser": "^1.2.7", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@types/readable-stream": "^4.0.21", "@types/seedrandom": "^3.0.8", "@types/send": "^0.17.4", "@types/serve-static": "^1.15.7", "@types/sinon": "^17.0.4", "@types/sinon-chai": "^4.0.0", "@types/statuses": "^2.0.5", "@types/stream-to-array": "^2.3.3", "@types/supertest": "^6.0.3", "@types/tape": "^5.8.1", "@types/toidentifier": "^1.0.2", "@types/type-is": "^1.6.7", "@types/uid-safe": "^2.1.5", "@types/util-deprecate": "^1.0.4", "@types/utils-merge": "^1.0.0", "@types/uuid": "^10.0.0", "@types/vary": "^1.1.3", "@types/vhost": "^3.0.9", "@types/whatwg-url": "^13.0.0", "@types/ws": "^8.18.1", "@types/xo": "^0.39.9"}}