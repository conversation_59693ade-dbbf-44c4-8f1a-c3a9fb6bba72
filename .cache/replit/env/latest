declare -gx PROMPT_DIRTRIM=2
declare -gx CFLAGS=''
declare -gx NIX_PATH=nixpkgs=/home/<USER>/.nix-defexpr/channels/nixpkgs-stable-24_05:/home/<USER>/.nix-defexpr/channels
declare -gx REPLIT_PID1_VERSION=0.0.0-2282521
declare -gx REPL_OWNER_ID=15722197
declare -gx REPLIT_ENVIRONMENT=production
declare -gx XDG_CACHE_HOME=/home/<USER>/workspace/.cache
declare -gx REPL_ID=304e5062-821d-42b4-8bf3-52cee6a14ffd
declare -gx GI_TYPELIB_PATH=''
declare -gx VITE_API_BASE_URL=https://chewy-ai.replit.app/api
declare -gx REPLIT_DOMAINS=304e5062-821d-42b4-8bf3-52cee6a14ffd-00-35plnljz3wwcd.worf.replit.dev
declare -gx GIT_EDITOR=replit-git-editor
declare -gx PKG_CONFIG_PATH_FOR_TARGET=''
declare -gx XDG_DATA_HOME=/home/<USER>/workspace/.local/share
declare -gx GIT_ASKPASS=replit-git-askpass
declare -gx NIX_PS1='\[\033[01;34m\]\w\[\033[00m\]\$ '
declare -gx LOCALE_ARCHIVE=/usr/lib/locale/locale-archive
declare -gx REPL_PUBKEYS='{"crosis-ci":"7YlpcYh82oR9NSTtSYtR5jDL4onNzCGJGq6b+9CuZII=","crosis-ci:1":"7YlpcYh82oR9NSTtSYtR5jDL4onNzCGJGq6b+9CuZII=","crosis-ci:latest":"7YlpcYh82oR9NSTtSYtR5jDL4onNzCGJGq6b+9CuZII=","prod":"tGsjlu/BJvWTgvMaX7acuUb7AO1dXOrRiuk7y083RFE=","prod:1":"tGsjlu/BJvWTgvMaX7acuUb7AO1dXOrRiuk7y083RFE=","prod:2":"8uGN+vfszlnV93/HCSHlVLG0xddMlPkir1Ni4JKT4+w=","prod:3":"9+MCOSHQSQlcodXoot8dC8NLhc862nLkx1/VMsbY2h8=","prod:4":"8uGN+vfszlnV93/HCSHlVLG0xddMlPkir1Ni4JKT4+w=","prod:5":"9+MCOSHQSQlcodXoot8dC8NLhc862nLkx1/VMsbY2h8=","prod:latest":"tGsjlu/BJvWTgvMaX7acuUb7AO1dXOrRiuk7y083RFE=","vault-goval-token":"D5jJoMx1Ml54HM92NLgXl+MzptwDqbSsfyFG6f52g9E=","vault-goval-token:1":"D5jJoMx1Ml54HM92NLgXl+MzptwDqbSsfyFG6f52g9E=","vault-goval-token:latest":"D5jJoMx1Ml54HM92NLgXl+MzptwDqbSsfyFG6f52g9E="}'
declare -gx REPLIT_DEV_DOMAIN=304e5062-821d-42b4-8bf3-52cee6a14ffd-00-35plnljz3wwcd.worf.replit.dev
declare -gx DOCKER_CONFIG=/home/<USER>/workspace/.config/docker
read -r _new_path <<< "/nix/store/hp7fmkb1rzmfxisj83c8f8dqz146nm6q-supabase-cli-1.168.1/bin:/nix/store/r2wrscmjzn4f2f7wk7q2ms2h96mjwzv7-npx/bin:/home/<USER>/workspace/.config/npm/node_global/bin:/home/<USER>/workspace/node_modules/.bin:/nix/store/rrz8cqhldyl17bbs60g7d8vbaadkxc40-nodejs-20.18.1-wrapped/bin:/nix/store/5q4dz85wgqhifng1fk2xy85pslkmiqvs-bun-1.2.12/bin:/nix/store/z8s3r4vwf4r26g2d7shnw5lva6ihim8f-pnpm-9.15.0/bin:/nix/store/jcgdksj946l5l42c2y9ks2l4g6n74h3f-yarn-1.22.22/bin:/nix/store/2s17mrby0ph00z22rkabfs9vzpzx1r70-prettier-3.3.3/bin:/nix/store/5lkb22xbsxbzih80f4pk1jyb9sp97aj5-pid1/bin:/nix/store/a3a2dskycxvn7cbrfb2nnska6a8xq1b8-replit-runtime-path/bin:/home/<USER>/.nix-profile/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
#PATH=/nix/store/hp7fmkb1rzmfxisj83c8f8dqz146nm6q-supabase-cli-1.168.1/bin:/nix/store/r2wrscmjzn4f2f7wk7q2ms2h96mjwzv7-npx/bin:/home/<USER>/workspace/.config/npm/node_global/bin:/home/<USER>/workspace/node_modules/.bin:/nix/store/rrz8cqhldyl17bbs60g7d8vbaadkxc40-nodejs-20.18.1-wrapped/bin:/nix/store/5q4dz85wgqhifng1fk2xy85pslkmiqvs-bun-1.2.12/bin:/nix/store/z8s3r4vwf4r26g2d7shnw5lva6ihim8f-pnpm-9.15.0/bin:/nix/store/jcgdksj946l5l42c2y9ks2l4g6n74h3f-yarn-1.22.22/bin:/nix/store/2s17mrby0ph00z22rkabfs9vzpzx1r70-prettier-3.3.3/bin:/nix/store/5lkb22xbsxbzih80f4pk1jyb9sp97aj5-pid1/bin:/nix/store/a3a2dskycxvn7cbrfb2nnska6a8xq1b8-replit-runtime-path/bin:/home/<USER>/.nix-profile/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
if [ -e "/run/replit/env/last" ]; then read -r _last_path < <(\grep '^#PATH=' /run/replit/env/last | cut -f 2 -d =); fi
_user_components="$(\tr : $'\n' <<< "${PATH:-}" |\grep -xv -f <(\tr : $'\n' <<< "${_last_path}") |\tr $'\n' :)"
declare -gx PATH="${_user_components}${_new_path}"
declare -gx REPL_OWNER=Chewy42
declare -gx REPLIT_CLUSTER=worf
declare -gx REPLIT_RTLD_LOADER=1
declare -gx DISPLAY=:0
declare -gx LIBGL_DRIVERS_PATH=/nix/store/1z62rda9iqnxi4ryvgmyvfaj979hgk7s-mesa-24.2.8-drivers/lib/dri
declare -gx XDG_CONFIG_HOME=/home/<USER>/workspace/.config
declare -gx PORT=5000
declare -gx USER=runner
declare -gx REPLIT_NIX_CHANNEL=stable-24_05
declare -gx NIX_CFLAGS_COMPILE=''
declare -gx PKG_CONFIG_PATH=''
declare -gx FRONTEND_PORT=80
declare -gx NIX_PROFILES='/nix/var/nix/profiles/default /home/<USER>/.nix-profile'
declare -gx REPLIT_CONTAINER=gcr.io/marine-cycle-160323/nix:bf8590a3e2f0a8b70b7ca175eeed9074dffbfca9
declare -gx LDFLAGS=''
declare -gx NIXPKGS_ALLOW_UNFREE=1
declare -gx LANG=en_US.UTF-8
declare -gx REPLIT_RIPPKGS_INDICES=/nix/store/l5gcmdp908sji4wchfp8csflhjcgnmm3-rippkgs-indices
declare -gx HOME=/home/<USER>
declare -gx REPL_HOME=/home/<USER>/workspace
declare -gx REPL_SLUG=workspace
declare -gx NIX_LDFLAGS=''
declare -gx HOSTNAME=15ff0687d802
declare -gx REPLIT_SUBCLUSTER=interactive
declare -gx REPL_LANGUAGE=nix
declare -gx REPL_IMAGE=gcr.io/marine-cycle-160323/nix:bf8590a3e2f0a8b70b7ca175eeed9074dffbfca9
declare -gx REPLIT_BASHRC=/nix/store/jww70qbp1jafzdjawqy5sj0hpsp9xc03-replit-bashrc/bashrc
declare -gx REPLIT_LD_AUDIT=/nix/store/n5x1kgbz8zjh63ymsijbislyi1n1hir6-replit_rtld_loader-1/rtld_loader.so
declare -gx XDG_DATA_DIRS=/nix/store/hp7fmkb1rzmfxisj83c8f8dqz146nm6q-supabase-cli-1.168.1/share:/nix/store/a3a2dskycxvn7cbrfb2nnska6a8xq1b8-replit-runtime-path/share
declare -gx COLORTERM=truecolor
declare -gx REPLIT_CLI=/nix/store/x20rg378hii3h2j9bn0516vkpphnr5cx-pid1-0.0.1/bin/replit
declare -gx npm_config_prefix=/home/<USER>/workspace/.config/npm/node_global
declare -gx __EGL_VENDOR_LIBRARY_FILENAMES=/nix/store/1z62rda9iqnxi4ryvgmyvfaj979hgk7s-mesa-24.2.8-drivers/share/glvnd/egl_vendor.d/50_mesa.json
declare -gx REPLIT_PID1_FLAG_REPLIT_RTLD_LOADER=1
