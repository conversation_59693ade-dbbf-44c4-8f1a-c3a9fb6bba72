[["72231a06-d7e3-4a8c-8389-2019ed201d60", {"value": {"selectedCode": "", "prefix": "# Content Security Policy (CSP) Fix for Google Fonts\n", "suffix": "\n## Problem Summary\n\nThe deployed application at `https://chewy-ai.replit.app/` was blocking Google Fonts with CSP violations:\n\n```\nRefused to load the stylesheet 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap' because it violates the following Content Security Policy directive: \"style-src 'self' 'unsafe-inline'\"\n```\n\n## Root Cause\n\n1. **CSP Policy Too Restrictive**: The Content Security Policy in `frontend-server/index.ts` was blocking external font resources\n2. **Deployment Issue**: Changes to source code were not reflected in the deployed build (`dist/frontend-server.js`)\n\n## Solution Applied\n\n### 1. Updated CSP Policy\n\n**Before (Blocking Google Fonts):**\n```javascript\n\"default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://replit.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' ws: wss: http://localhost:5000 https:;\"\n```\n\n**After (Allowing Google Fonts):**\n```javascript\n\"default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://replit.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: https:; font-src 'self' data: https://fonts.gstatic.com; connect-src 'self' ws: wss: http://localhost:5000 https:;\"\n```\n\n### 2. Key Changes\n\n- **`style-src`**: Added `https://fonts.googleapis.com` to allow Google Fonts CSS\n- **`font-src`**: Added `https://fonts.gstatic.com` to allow Google Fonts files\n\n### 3. Added Debug Logging\n\n```javascript\nconst cspPolicy = \"...\";\nres.setHeader(\"Content-Security-Policy\", cspPolicy);\n\n// Debug logging for CSP policy\nif (process.env.NODE_ENV === \"production\") {\n  console.log(\"🔒 CSP Policy set:\", cspPolicy.includes(\"fonts.googleapis.com\") ? \"✅ Google Fonts allowed\" : \"❌ Google Fonts blocked\");\n}\n```\n\n## Deployment Requirements\n\n**CRITICAL**: After making CSP changes, you must:\n\n1. **Rebuild the application**:\n   ```bash\n   npm run build\n   ```\n\n2. **Redeploy to Replit**:\n   - Trigger a new deployment through Replit interface\n   - Or push changes to trigger auto-deployment\n\n3. **Verify the fix**:\n   - Check browser console for CSP errors\n   - Verify fonts are loading correctly\n   - Check deployment logs for \"✅ Google Fonts allowed\" message\n\n## How Google Fonts Work with CSP\n\nGoogle Fonts requires two types of requests:\n\n1. **CSS Stylesheet Request** → `https://fonts.googleapis.com/css2?family=...`\n   - Returns CSS with `@font-face` declarations\n   - Requires `style-src` permission\n\n2. **Font File Requests** → `https://fonts.gstatic.com/s/inter/...`\n   - Downloads actual font files (WOFF2, WOFF, TTF, etc.)\n   - Requires `font-src` permission\n\n## Security Analysis\n\nThe updated CSP policy maintains security:\n\n✅ **Secure Additions:**\n- Only allows Google's official domains (`fonts.googleapis.com`, `fonts.gstatic.com`)\n- Uses HTTPS-only connections\n- No wildcard domains that could be exploited\n\n✅ **Maintained Security:**\n- Still blocks arbitrary external stylesheets and fonts\n- Prevents XSS via style injection from untrusted sources\n- Only allows specific, trusted domains\n\n## Verification Steps\n\n### 1. Check Browser Console\n- No CSP violation errors for Google Fonts\n- Fonts loading successfully\n\n### 2. Inspect Network Tab\n- Successful requests to `fonts.googleapis.com`\n- Successful requests to `fonts.gstatic.com`\n\n### 3. Check Response Headers\n- Verify CSP header includes Google Fonts domains\n- Look for debug log: \"✅ Google Fonts allowed\"\n\n### 4. Visual Verification\n- Inter and Roboto fonts displaying correctly\n- Material Icons rendering properly\n\n## Troubleshooting\n\n### If CSP Errors Persist:\n\n1. **Check if rebuild was successful**:\n   ```bash\n   grep -n \"fonts.googleapis.com\" dist/frontend-server.js\n   ```\n\n2. **Verify deployment logs**:\n   - Look for \"🔒 CSP Policy set: ✅ Google Fonts allowed\"\n   - Check for any build errors\n\n3. **Clear browser cache**:\n   - Hard refresh (Ctrl+F5 / Cmd+Shift+R)\n   - Clear site data in DevTools\n\n4. **Check for conflicting CSP headers**:\n   - Inspect Network tab → Response Headers\n   - Look for multiple CSP headers\n\n### Alternative Solutions\n\nIf the current approach doesn't work:\n\n1. **Self-host Google Fonts**:\n   - Download font files and serve from your domain\n   - Update CSS to reference local files\n\n2. **Use font-display: swap**:\n   - Improve loading experience\n   - Add fallback fonts\n\n3. **Preconnect hints**:\n   ```html\n   <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">\n   <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>\n   ```\n\n## Files Modified\n\n- `frontend-server/index.ts` - Updated CSP policy and added debug logging\n- `docs/CSP_GOOGLE_FONTS_FIX.md` - This documentation\n\n## Related Issues\n\n- Port mapping configuration (resolved)\n- Proxy connection errors (resolved)\n- Build and deployment process\n\n## Next Steps\n\n1. **Rebuild and redeploy** the application\n2. **Verify** Google Fonts are loading without CSP errors\n3. **Monitor** deployment logs for CSP debug messages\n4. **Test** all font families (Inter, Roboto, Material Icons)\n", "path": "docs/CSP_GOOGLE_FONTS_FIX.md", "language": "markdown", "prefixBegin": 0, "suffixEnd": 0}}], ["12485823-5920-4eef-9fa5-955342d7e626", {"value": {"selectedCode": "", "prefix": "// Load environment variables FIRST, before any other imports\n", "suffix": "import path from \"path\";\nimport { fileURLToPath } from \"url\";\nimport { config as dotenvConfig } from \"dotenv\";\n\n// Get current file's directory in ES modules\nconst __filename = fileURLToPath(import.meta.url);\nconst __dirname = path.dirname(__filename);\n\n// Load environment variables immediately in development\nif (process.env.NODE_ENV !== \"production\") {\n  const envPath = path.resolve(__dirname, \"../.env\");\n  dotenvConfig({ path: envPath });\n}\n\nimport express, { type Request, Response, NextFunction } from \"express\";\nimport cors from \"cors\";\nimport { Readable } from \"stream\"; // Import Readable\n\n// Extend RequestInit to include Node.js specific duplex option\ninterface NodeRequestInit extends RequestInit {\n  duplex?: \"half\" | \"full\";\n}\n\n// Route imports moved to after environment loading\n\nconst app = express();\n\n// Disable Express's ETag headers for API routes to avoid 304 responses\napp.disable(\"etag\");\n\n// Ensure dynamic API responses are never cached\napp.use((req, res, next) => {\n  if (req.path.startsWith(\"/api\")) {\n    res.setHeader(\"Cache-Control\", \"no-store\");\n  }\n  next();\n});\n\n// CORS configuration for frontend-backend separation\napp.use(\n  cors({\n    // Allow requests from the frontend server\n    origin: process.env.NODE_ENV === \"production\"\n      ? [\"http://localhost:3000\", process.env.FRONTEND_URL].filter(Boolean)\n      : [\"http://localhost:3000\", \"http://localhost:5173\", process.env.FRONTEND_URL].filter(Boolean),\n    credentials: true,\n    methods: [\"GET\", \"POST\", \"PUT\", \"DELETE\", \"OPTIONS\"],\n    allowedHeaders: [\"Content-Type\", \"Authorization\"],\n  })\n);\n\napp.use(express.json({ limit: \"10mb\" }));\napp.use(express.urlencoded({ extended: false }));\n\n// Debug middleware to log API requests\napp.use((req, res, next) => {\n  if (req.path.startsWith(\"/api\")) {\n    console.log(\n      `${req.method} ${req.path} - Body keys: ${Object.keys(req.body).join(\n        \", \"\n      )}`\n    );\n  }\n  next();\n});\n\n// Serve static files in production - moved to async function below\n\napp.use((req, res, next) => {\n  const start = Date.now();\n  const path = req.path;\n  let capturedJsonResponse: Record<string, any> | undefined = undefined;\n\n  const originalResJson = res.json;\n  res.json = function (bodyJson, ...args) {\n    capturedJsonResponse = bodyJson;\n    return originalResJson.apply(res, [bodyJson, ...args]);\n  };\n\n  res.on(\"finish\", () => {\n    const duration = Date.now() - start;\n    if (path.startsWith(\"/api\")) {\n      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;\n      if (capturedJsonResponse) {\n        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;\n      }\n\n      if (logLine.length > 80) {\n        logLine = logLine.slice(0, 79) + \"…\";\n      }\n\n      console.log(logLine);\n    }\n  });\n\n  next();\n});\n\n// Route mounting moved to async function after environment loading\n\n// Helper function to convert Node.js stream to Web stream if needed\nasync function nodeStreamToWebStream(\n  nodeStream: NodeJS.ReadableStream\n): Promise<ReadableStream<Uint8Array>> {\n  return new ReadableStream({\n    start(controller) {\n      nodeStream.on(\"data\", (chunk) =>\n        controller.enqueue(new Uint8Array(chunk))\n      );\n      nodeStream.on(\"end\", () => controller.close());\n      nodeStream.on(\"error\", (err) => controller.error(err));\n    },\n  });\n}\n\n// Adapter middleware for Hono to Express\nconst honoAdapter = (honoApp: any, basePath: string = \"\") => {\n  return async (\n    req: express.Request,\n    res: express.Response,\n    next: express.NextFunction\n  ) => {\n    try {\n      const hasBody = req.method !== \"GET\" && req.method !== \"HEAD\" && req.body;\n      const requestBody = hasBody\n        ? req.body instanceof Readable\n          ? await nodeStreamToWebStream(req.body)\n          : new ReadableStream({\n              start: (controller) => {\n                controller.enqueue(Buffer.from(JSON.stringify(req.body)));\n                controller.close();\n              },\n            })\n        : undefined;\n\n      // Strip the base path from the URL for Hono\n      let honoPath = req.url;\n      if (basePath && req.url.startsWith(basePath)) {\n        honoPath = req.url.substring(basePath.length) || \"/\";\n      }\n\n      const webRequest = new Request(\n        `${req.protocol}://${req.get(\"host\")}${honoPath}`,\n        {\n          method: req.method,\n          headers: new Headers(req.headers as HeadersInit),\n          body: requestBody,\n          // Required for Node.js 18+ when sending a body\n          duplex: hasBody ? \"half\" : undefined,\n        } as NodeRequestInit\n      );\n\n      console.log(`[honoAdapter] Forwarding ${webRequest.method} ${webRequest.url} to Hono app. Path for Hono: ${honoPath}`);\n      const webResponse = await honoApp.fetch(webRequest);\n\n      res.status(webResponse.status);\n      webResponse.headers.forEach((value: string, key: string) => {\n        res.setHeader(key, value);\n      });\n\n      if (webResponse.body) {\n        const reader = webResponse.body.getReader();\n        const forwardStream = async () => {\n          while (true) {\n            const { done, value } = await reader.read();\n            if (done) break;\n            res.write(value);\n          }\n          res.end();\n        };\n        await forwardStream();\n      } else {\n        res.end();\n      }\n    } catch (error) {\n      next(error);\n    }\n  };\n};\n\n// All route mounting moved to async function after environment loading\n\n(async () => {\n  console.log(\"🚀 Starting server initialization...\");\n\n  // Environment variables already loaded at the top of the file\n\n  // Now import routes after environment variables are loaded\n  const { registerRoutes } = await import(\"./routes\");\n  const { log } = await import(\"./vite\");\n  const aiRoutes = (await import(\"./routes/aiRoutes\")).default;\n  const quizRoutes = (await import(\"./routes/quizRoutes\")).default;\n  const documentRoutes = (await import(\"./routes/documentRoutes\")).default;\n  const flashcardDeckRoutes = (await import(\"./routes/flashcardDeckRoutes\")).default;\n  const flashcardRoutes = (await import(\"./routes/flashcardRoutes\")).default;\n  const flashcardSetRoutes = (await import(\"./routes/flashcardSetRoutes\")).default;\n  const testRoutes = (await import(\"./routes/testRoutes\")).default;\n  const healthRoutes = (await import(\"./routes/healthRoutes\")).default;\n  const credentialsRoutes = (await import(\"./routes/credentialsRoutes\")).default;\n  const authRoutes = (await import(\"./routes/authRoutes\")).default;\n\n  // Mount routes after imports\n  console.log(\"🔧 Mounting AI routes at /api\");\n  app.use(\"/api\", aiRoutes);\n  console.log(\"🔧 Mounting flashcard deck routes at /api/decks\");\n  app.use(\"/api/decks\", flashcardDeckRoutes);\n  console.log(\"🔧 Mounting test routes at /api\");\n  app.use(\"/api\", testRoutes);\n  console.log(\"🔧 Mounting health check routes at /api/health\");\n  app.use(\"/api/health\", healthRoutes);\n  console.log(\"🔧 Mounting credentials routes at /api/credentials\");\n  app.use(\"/api/credentials\", credentialsRoutes);\n  console.log(\"🔧 Mounting auth routes at /api/auth\");\n  app.use(\"/api/auth\", authRoutes);\n\n  // Mount Express routes directly\n  console.log(\"🔧 Mounting document routes at /api/documents\");\n  app.use(\"/api/documents\", documentRoutes);\n\n  // Mount Hono routes with the adapter\n  console.log(\"🔧 Mounting quiz routes at /api/quizzes\");\n  app.use(\"/api/quizzes\", honoAdapter(quizRoutes, \"/api/quizzes\"));\n\n  console.log(\"🔧 Mounting flashcard set routes at /api/flashcard-sets\");\n  app.use(\"/api/flashcard-sets\", honoAdapter(flashcardSetRoutes, \"/api/flashcard-sets\"));\n\n  console.log(\"🔧 Mounting individual flashcard routes at /api/flashcards\");\n  app.use(\"/api/flashcards\", honoAdapter(flashcardRoutes, \"/api/flashcards\"));\n\n  try {\n\n    const server = await registerRoutes(app);\n    console.log(\"✅ Routes registered successfully\");\n\n    app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {\n      console.error(\"Unhandled error in Main Express App:\", err);\n      const status = err.status || err.statusCode || 500;\n      const responseBody: { error: string; message: string; stack?: string } = {\n        error: \"An unexpected server error occurred.\",\n        message: err.message || \"Internal Server Error\",\n      };\n      // Optionally include stack in development\n      if (app.get(\"env\") === \"development\" && err.stack) {\n        responseBody.stack = err.stack;\n      }\n      res.status(status).json(responseBody);\n      // Error is logged and JSON response sent. Do not call _next(err) or throw err to prevent Vite's HTML error.\n    });\n\n\n\n    // Get port from environment variable or use default\n    // In production (Replit deployment), use port 80\n    // In development, use port 5000\n    const port = parseInt(process.env.PORT || \"5000\", 10);\n    const isWindows = process.platform === \"win32\";\n\n    log(`Starting server in ${process.env.NODE_ENV || 'development'} mode on port ${port}`);\n\n    // Log environment info for debugging\n    log(`Environment: NODE_ENV=${process.env.NODE_ENV}, PORT=${process.env.PORT}`);\n    log(`API Base URL will be: ${process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:5000/api'}`);\n\n    const httpServer = server.listen(\n      {\n        port,\n        host: \"0.0.0.0\",\n        ...(isWindows ? {} : { reusePort: true }),\n      },\n      () => {\n        log(`✅ Backend server running on port ${port}`);\n        log(`🌐 API available at: http://localhost:${port}/api`);\n        log(`🔍 Health check: http://localhost:${port}/api/health`);\n        log(`🐛 Debug routes: http://localhost:${port}/api/debug/routes`);\n      }\n    );\n    \n    // Handle graceful shutdown\n    const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];\n    signals.forEach((signal) => {\n      process.on(signal, () => {\n        log(`Received ${signal}, gracefully shutting down...`);\n        \n        // Set a timeout for forceful shutdown if graceful shutdown takes too long\n        const forcefulShutdownTimeout = setTimeout(() => {\n          log('Forceful shutdown timeout reached, exiting immediately!');\n          process.exit(1);\n        }, 30000); // 30 seconds timeout\n        \n        // Attempt graceful shutdown\n        httpServer.close(() => {\n          log('HTTP server closed successfully.');\n          clearTimeout(forcefulShutdownTimeout);\n          process.exit(0);\n        });\n      });\n    });\n  } catch (error: any) {\n    console.error(\"❌ Failed to start server:\", error);\n    console.error(\"Error details:\", error.message);\n    console.error(\"Stack trace:\", error.stack);\n    process.exit(1);\n  }\n})();\n", "path": "server/index.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["9e4f5d05-0d9e-4661-9e8d-f6c26778199e", {"value": {"selectedCode": "", "prefix": "// Load environment variables FIRST, before any other imports\nimport path from \"path\";\nimport { fileURLToPath } from \"url\";\nimport { config as dotenvConfig } from \"dotenv\";\n\n// Get current file's directory in ES modules\nconst __filename = fileURLToPath(import.meta.url);\nconst __dirname = path.dirname(__filename);\n\n// Load environment variables immediately in development\nif (process.env.NODE_ENV !== \"production\") {\n  const envPath = path.resolve(__dirname, \"../.env\");\n  dotenvConfig({ path: envPath });\n}\n\nimport express, { type Request, Response, NextFunction } from \"express\";\nimport cors from \"cors\";\nimport { Readable } from \"stream\"; // Import Readable\n\n// Extend RequestInit to include Node.js specific duplex option\ninterface NodeRequestInit extends RequestInit {\n  duplex?: \"half\" | \"full\";\n}\n\n// Route imports moved to after environment loading\n\nconst app = express();\n\n// Disable Express's ETag headers for API routes to avoid 304 responses\napp.disable(\"etag\");\n\n", "suffix": "// Ensure dynamic API responses are never cached\napp.use((req, res, next) => {\n  if (req.path.startsWith(\"/api\")) {\n    res.setHeader(\"Cache-Control\", \"no-store\");\n  }\n  next();\n});\n\n// CORS configuration for frontend-backend separation\napp.use(\n  cors({\n    // Allow requests from the frontend server\n    origin: process.env.NODE_ENV === \"production\"\n      ? [\"http://localhost:3000\", process.env.FRONTEND_URL].filter(Boolean)\n      : [\"http://localhost:3000\", \"http://localhost:5173\", process.env.FRONTEND_URL].filter(Boolean),\n    credentials: true,\n    methods: [\"GET\", \"POST\", \"PUT\", \"DELETE\", \"OPTIONS\"],\n    allowedHeaders: [\"Content-Type\", \"Authorization\"],\n  })\n);\n\napp.use(express.json({ limit: \"10mb\" }));\napp.use(express.urlencoded({ extended: false }));\n\n// Debug middleware to log API requests\napp.use((req, res, next) => {\n  if (req.path.startsWith(\"/api\")) {\n    console.log(\n      `${req.method} ${req.path} - Body keys: ${Object.keys(req.body).join(\n        \", \"\n      )}`\n    );\n  }\n  next();\n});\n\n// Serve static files in production - moved to async function below\n\napp.use((req, res, next) => {\n  const start = Date.now();\n  const path = req.path;\n  let capturedJsonResponse: Record<string, any> | undefined = undefined;\n\n  const originalResJson = res.json;\n  res.json = function (bodyJson, ...args) {\n    capturedJsonResponse = bodyJson;\n    return originalResJson.apply(res, [bodyJson, ...args]);\n  };\n\n  res.on(\"finish\", () => {\n    const duration = Date.now() - start;\n    if (path.startsWith(\"/api\")) {\n      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;\n      if (capturedJsonResponse) {\n        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;\n      }\n\n      if (logLine.length > 80) {\n        logLine = logLine.slice(0, 79) + \"…\";\n      }\n\n      console.log(logLine);\n    }\n  });\n\n  next();\n});\n\n// Route mounting moved to async function after environment loading\n\n// Helper function to convert Node.js stream to Web stream if needed\nasync function nodeStreamToWebStream(\n  nodeStream: NodeJS.ReadableStream\n): Promise<ReadableStream<Uint8Array>> {\n  return new ReadableStream({\n    start(controller) {\n      nodeStream.on(\"data\", (chunk) =>\n        controller.enqueue(new Uint8Array(chunk))\n      );\n      nodeStream.on(\"end\", () => controller.close());\n      nodeStream.on(\"error\", (err) => controller.error(err));\n    },\n  });\n}\n\n// Adapter middleware for Hono to Express\nconst honoAdapter = (honoApp: any, basePath: string = \"\") => {\n  return async (\n    req: express.Request,\n    res: express.Response,\n    next: express.NextFunction\n  ) => {\n    try {\n      const hasBody = req.method !== \"GET\" && req.method !== \"HEAD\" && req.body;\n      const requestBody = hasBody\n        ? req.body instanceof Readable\n          ? await nodeStreamToWebStream(req.body)\n          : new ReadableStream({\n              start: (controller) => {\n                controller.enqueue(Buffer.from(JSON.stringify(req.body)));\n                controller.close();\n              },\n            })\n        : undefined;\n\n      // Strip the base path from the URL for Hono\n      let honoPath = req.url;\n      if (basePath && req.url.startsWith(basePath)) {\n        honoPath = req.url.substring(basePath.length) || \"/\";\n      }\n\n      const webRequest = new Request(\n        `${req.protocol}://${req.get(\"host\")}${honoPath}`,\n        {\n          method: req.method,\n          headers: new Headers(req.headers as HeadersInit),\n          body: requestBody,\n          // Required for Node.js 18+ when sending a body\n          duplex: hasBody ? \"half\" : undefined,\n        } as NodeRequestInit\n      );\n\n      console.log(`[honoAdapter] Forwarding ${webRequest.method} ${webRequest.url} to Hono app. Path for Hono: ${honoPath}`);\n      const webResponse = await honoApp.fetch(webRequest);\n\n      res.status(webResponse.status);\n      webResponse.headers.forEach((value: string, key: string) => {\n        res.setHeader(key, value);\n      });\n\n      if (webResponse.body) {\n        const reader = webResponse.body.getReader();\n        const forwardStream = async () => {\n          while (true) {\n            const { done, value } = await reader.read();\n            if (done) break;\n            res.write(value);\n          }\n          res.end();\n        };\n        await forwardStream();\n      } else {\n        res.end();\n      }\n    } catch (error) {\n      next(error);\n    }\n  };\n};\n\n// All route mounting moved to async function after environment loading\n\n(async () => {\n  console.log(\"🚀 Starting server initialization...\");\n\n  // Environment variables already loaded at the top of the file\n\n  // Now import routes after environment variables are loaded\n  const { registerRoutes } = await import(\"./routes\");\n  const { log } = await import(\"./vite\");\n  const aiRoutes = (await import(\"./routes/aiRoutes\")).default;\n  const quizRoutes = (await import(\"./routes/quizRoutes\")).default;\n  const documentRoutes = (await import(\"./routes/documentRoutes\")).default;\n  const flashcardDeckRoutes = (await import(\"./routes/flashcardDeckRoutes\")).default;\n  const flashcardRoutes = (await import(\"./routes/flashcardRoutes\")).default;\n  const flashcardSetRoutes = (await import(\"./routes/flashcardSetRoutes\")).default;\n  const testRoutes = (await import(\"./routes/testRoutes\")).default;\n  const healthRoutes = (await import(\"./routes/healthRoutes\")).default;\n  const credentialsRoutes = (await import(\"./routes/credentialsRoutes\")).default;\n  const authRoutes = (await import(\"./routes/authRoutes\")).default;\n\n  // Mount routes after imports\n  console.log(\"🔧 Mounting AI routes at /api\");\n  app.use(\"/api\", aiRoutes);\n  console.log(\"🔧 Mounting flashcard deck routes at /api/decks\");\n  app.use(\"/api/decks\", flashcardDeckRoutes);\n  console.log(\"🔧 Mounting test routes at /api\");\n  app.use(\"/api\", testRoutes);\n  console.log(\"🔧 Mounting health check routes at /api/health\");\n  app.use(\"/api/health\", healthRoutes);\n  console.log(\"🔧 Mounting credentials routes at /api/credentials\");\n  app.use(\"/api/credentials\", credentialsRoutes);\n  console.log(\"🔧 Mounting auth routes at /api/auth\");\n  app.use(\"/api/auth\", authRoutes);\n\n  // Mount Express routes directly\n  console.log(\"🔧 Mounting document routes at /api/documents\");\n  app.use(\"/api/documents\", documentRoutes);\n\n  // Mount Hono routes with the adapter\n  console.log(\"🔧 Mounting quiz routes at /api/quizzes\");\n  app.use(\"/api/quizzes\", honoAdapter(quizRoutes, \"/api/quizzes\"));\n\n  console.log(\"🔧 Mounting flashcard set routes at /api/flashcard-sets\");\n  app.use(\"/api/flashcard-sets\", honoAdapter(flashcardSetRoutes, \"/api/flashcard-sets\"));\n\n  console.log(\"🔧 Mounting individual flashcard routes at /api/flashcards\");\n  app.use(\"/api/flashcards\", honoAdapter(flashcardRoutes, \"/api/flashcards\"));\n\n  try {\n\n    const server = await registerRoutes(app);\n    console.log(\"✅ Routes registered successfully\");\n\n    app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {\n      console.error(\"Unhandled error in Main Express App:\", err);\n      const status = err.status || err.statusCode || 500;\n      const responseBody: { error: string; message: string; stack?: string } = {\n        error: \"An unexpected server error occurred.\",\n        message: err.message || \"Internal Server Error\",\n      };\n      // Optionally include stack in development\n      if (app.get(\"env\") === \"development\" && err.stack) {\n        responseBody.stack = err.stack;\n      }\n      res.status(status).json(responseBody);\n      // Error is logged and JSON response sent. Do not call _next(err) or throw err to prevent Vite's HTML error.\n    });\n\n\n\n    // Get port from environment variable or use default\n    // In production (Replit deployment), use port 80\n    // In development, use port 5000\n    const port = parseInt(process.env.PORT || \"5000\", 10);\n    const isWindows = process.platform === \"win32\";\n\n    log(`Starting server in ${process.env.NODE_ENV || 'development'} mode on port ${port}`);\n\n    // Log environment info for debugging\n    log(`Environment: NODE_ENV=${process.env.NODE_ENV}, PORT=${process.env.PORT}`);\n    log(`API Base URL will be: ${process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:5000/api'}`);\n\n    const httpServer = server.listen(\n      {\n        port,\n        host: \"0.0.0.0\",\n        ...(isWindows ? {} : { reusePort: true }),\n      },\n      () => {\n        log(`✅ Backend server running on port ${port}`);\n        log(`🌐 API available at: http://localhost:${port}/api`);\n        log(`🔍 Health check: http://localhost:${port}/api/health`);\n        log(`🐛 Debug routes: http://localhost:${port}/api/debug/routes`);\n      }\n    );\n    \n    // Handle graceful shutdown\n    const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];\n    signals.forEach((signal) => {\n      process.on(signal, () => {\n        log(`Received ${signal}, gracefully shutting down...`);\n        \n        // Set a timeout for forceful shutdown if graceful shutdown takes too long\n        const forcefulShutdownTimeout = setTimeout(() => {\n          log('Forceful shutdown timeout reached, exiting immediately!');\n          process.exit(1);\n        }, 30000); // 30 seconds timeout\n        \n        // Attempt graceful shutdown\n        httpServer.close(() => {\n          log('HTTP server closed successfully.');\n          clearTimeout(forcefulShutdownTimeout);\n          process.exit(0);\n        });\n      });\n    });\n  } catch (error: any) {\n    console.error(\"❌ Failed to start server:\", error);\n    console.error(\"Error details:\", error.message);\n    console.error(\"Stack trace:\", error.stack);\n    process.exit(1);\n  }\n})();\n", "path": "server/index.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["a4d09b20-deb9-4690-8edb-4de9e1ae6884", {"value": {"selectedCode": "", "prefix": "// Load environment variables FIRST, before any other imports\nimport path from \"path\";\nimport { fileURLToPath } from \"url\";\nimport { config as dotenvConfig } from \"dotenv\";\n\n// Get current file's directory in ES modules\nconst __filename = fileURLToPath(import.meta.url);\nconst __dirname = path.dirname(__filename);\n\n// Load environment variables immediately in development\nif (process.env.NODE_ENV !== \"production\") {\n  const envPath = path.resolve(__dirname, \"../.env\");\n  dotenvConfig({ path: envPath });\n}\n\nimport express, { type Request, Response, NextFunction } from \"express\";\nimport cors from \"cors\";\nimport { Readable } from \"stream\"; // Import Readable\n\n// Extend RequestInit to include Node.js specific duplex option\ninterface NodeRequestInit extends RequestInit {\n  duplex?: \"half\" | \"full\";\n}\n\n// Route imports moved to after environment loading\n\nconst app = express();\n\n// Disable Express's ETag headers for API routes to avoid 304 responses\napp.disable(\"etag\");\n\n// Ensure dynamic API responses are never cached\n", "suffix": "app.use((req, res, next) => {\n  if (req.path.startsWith(\"/api\")) {\n    res.setHeader(\"Cache-Control\", \"no-store\");\n  }\n  next();\n});\n\n// CORS configuration for frontend-backend separation\napp.use(\n  cors({\n    // Allow requests from the frontend server\n    origin: process.env.NODE_ENV === \"production\"\n      ? [\"http://localhost:3000\", process.env.FRONTEND_URL].filter(Boolean)\n      : [\"http://localhost:3000\", \"http://localhost:5173\", process.env.FRONTEND_URL].filter(Boolean),\n    credentials: true,\n    methods: [\"GET\", \"POST\", \"PUT\", \"DELETE\", \"OPTIONS\"],\n    allowedHeaders: [\"Content-Type\", \"Authorization\"],\n  })\n);\n\napp.use(express.json({ limit: \"10mb\" }));\napp.use(express.urlencoded({ extended: false }));\n\n// Debug middleware to log API requests\napp.use((req, res, next) => {\n  if (req.path.startsWith(\"/api\")) {\n    console.log(\n      `${req.method} ${req.path} - Body keys: ${Object.keys(req.body).join(\n        \", \"\n      )}`\n    );\n  }\n  next();\n});\n\n// Serve static files in production - moved to async function below\n\napp.use((req, res, next) => {\n  const start = Date.now();\n  const path = req.path;\n  let capturedJsonResponse: Record<string, any> | undefined = undefined;\n\n  const originalResJson = res.json;\n  res.json = function (bodyJson, ...args) {\n    capturedJsonResponse = bodyJson;\n    return originalResJson.apply(res, [bodyJson, ...args]);\n  };\n\n  res.on(\"finish\", () => {\n    const duration = Date.now() - start;\n    if (path.startsWith(\"/api\")) {\n      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;\n      if (capturedJsonResponse) {\n        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;\n      }\n\n      if (logLine.length > 80) {\n        logLine = logLine.slice(0, 79) + \"…\";\n      }\n\n      console.log(logLine);\n    }\n  });\n\n  next();\n});\n\n// Route mounting moved to async function after environment loading\n\n// Helper function to convert Node.js stream to Web stream if needed\nasync function nodeStreamToWebStream(\n  nodeStream: NodeJS.ReadableStream\n): Promise<ReadableStream<Uint8Array>> {\n  return new ReadableStream({\n    start(controller) {\n      nodeStream.on(\"data\", (chunk) =>\n        controller.enqueue(new Uint8Array(chunk))\n      );\n      nodeStream.on(\"end\", () => controller.close());\n      nodeStream.on(\"error\", (err) => controller.error(err));\n    },\n  });\n}\n\n// Adapter middleware for Hono to Express\nconst honoAdapter = (honoApp: any, basePath: string = \"\") => {\n  return async (\n    req: express.Request,\n    res: express.Response,\n    next: express.NextFunction\n  ) => {\n    try {\n      const hasBody = req.method !== \"GET\" && req.method !== \"HEAD\" && req.body;\n      const requestBody = hasBody\n        ? req.body instanceof Readable\n          ? await nodeStreamToWebStream(req.body)\n          : new ReadableStream({\n              start: (controller) => {\n                controller.enqueue(Buffer.from(JSON.stringify(req.body)));\n                controller.close();\n              },\n            })\n        : undefined;\n\n      // Strip the base path from the URL for Hono\n      let honoPath = req.url;\n      if (basePath && req.url.startsWith(basePath)) {\n        honoPath = req.url.substring(basePath.length) || \"/\";\n      }\n\n      const webRequest = new Request(\n        `${req.protocol}://${req.get(\"host\")}${honoPath}`,\n        {\n          method: req.method,\n          headers: new Headers(req.headers as HeadersInit),\n          body: requestBody,\n          // Required for Node.js 18+ when sending a body\n          duplex: hasBody ? \"half\" : undefined,\n        } as NodeRequestInit\n      );\n\n      console.log(`[honoAdapter] Forwarding ${webRequest.method} ${webRequest.url} to Hono app. Path for Hono: ${honoPath}`);\n      const webResponse = await honoApp.fetch(webRequest);\n\n      res.status(webResponse.status);\n      webResponse.headers.forEach((value: string, key: string) => {\n        res.setHeader(key, value);\n      });\n\n      if (webResponse.body) {\n        const reader = webResponse.body.getReader();\n        const forwardStream = async () => {\n          while (true) {\n            const { done, value } = await reader.read();\n            if (done) break;\n            res.write(value);\n          }\n          res.end();\n        };\n        await forwardStream();\n      } else {\n        res.end();\n      }\n    } catch (error) {\n      next(error);\n    }\n  };\n};\n\n// All route mounting moved to async function after environment loading\n\n(async () => {\n  console.log(\"🚀 Starting server initialization...\");\n\n  // Environment variables already loaded at the top of the file\n\n  // Now import routes after environment variables are loaded\n  const { registerRoutes } = await import(\"./routes\");\n  const { log } = await import(\"./vite\");\n  const aiRoutes = (await import(\"./routes/aiRoutes\")).default;\n  const quizRoutes = (await import(\"./routes/quizRoutes\")).default;\n  const documentRoutes = (await import(\"./routes/documentRoutes\")).default;\n  const flashcardDeckRoutes = (await import(\"./routes/flashcardDeckRoutes\")).default;\n  const flashcardRoutes = (await import(\"./routes/flashcardRoutes\")).default;\n  const flashcardSetRoutes = (await import(\"./routes/flashcardSetRoutes\")).default;\n  const testRoutes = (await import(\"./routes/testRoutes\")).default;\n  const healthRoutes = (await import(\"./routes/healthRoutes\")).default;\n  const credentialsRoutes = (await import(\"./routes/credentialsRoutes\")).default;\n  const authRoutes = (await import(\"./routes/authRoutes\")).default;\n\n  // Mount routes after imports\n  console.log(\"🔧 Mounting AI routes at /api\");\n  app.use(\"/api\", aiRoutes);\n  console.log(\"🔧 Mounting flashcard deck routes at /api/decks\");\n  app.use(\"/api/decks\", flashcardDeckRoutes);\n  console.log(\"🔧 Mounting test routes at /api\");\n  app.use(\"/api\", testRoutes);\n  console.log(\"🔧 Mounting health check routes at /api/health\");\n  app.use(\"/api/health\", healthRoutes);\n  console.log(\"🔧 Mounting credentials routes at /api/credentials\");\n  app.use(\"/api/credentials\", credentialsRoutes);\n  console.log(\"🔧 Mounting auth routes at /api/auth\");\n  app.use(\"/api/auth\", authRoutes);\n\n  // Mount Express routes directly\n  console.log(\"🔧 Mounting document routes at /api/documents\");\n  app.use(\"/api/documents\", documentRoutes);\n\n  // Mount Hono routes with the adapter\n  console.log(\"🔧 Mounting quiz routes at /api/quizzes\");\n  app.use(\"/api/quizzes\", honoAdapter(quizRoutes, \"/api/quizzes\"));\n\n  console.log(\"🔧 Mounting flashcard set routes at /api/flashcard-sets\");\n  app.use(\"/api/flashcard-sets\", honoAdapter(flashcardSetRoutes, \"/api/flashcard-sets\"));\n\n  console.log(\"🔧 Mounting individual flashcard routes at /api/flashcards\");\n  app.use(\"/api/flashcards\", honoAdapter(flashcardRoutes, \"/api/flashcards\"));\n\n  try {\n\n    const server = await registerRoutes(app);\n    console.log(\"✅ Routes registered successfully\");\n\n    app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {\n      console.error(\"Unhandled error in Main Express App:\", err);\n      const status = err.status || err.statusCode || 500;\n      const responseBody: { error: string; message: string; stack?: string } = {\n        error: \"An unexpected server error occurred.\",\n        message: err.message || \"Internal Server Error\",\n      };\n      // Optionally include stack in development\n      if (app.get(\"env\") === \"development\" && err.stack) {\n        responseBody.stack = err.stack;\n      }\n      res.status(status).json(responseBody);\n      // Error is logged and JSON response sent. Do not call _next(err) or throw err to prevent Vite's HTML error.\n    });\n\n\n\n    // Get port from environment variable or use default\n    // In production (Replit deployment), use port 80\n    // In development, use port 5000\n    const port = parseInt(process.env.PORT || \"5000\", 10);\n    const isWindows = process.platform === \"win32\";\n\n    log(`Starting server in ${process.env.NODE_ENV || 'development'} mode on port ${port}`);\n\n    // Log environment info for debugging\n    log(`Environment: NODE_ENV=${process.env.NODE_ENV}, PORT=${process.env.PORT}`);\n    log(`API Base URL will be: ${process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:5000/api'}`);\n\n    const httpServer = server.listen(\n      {\n        port,\n        host: \"0.0.0.0\",\n        ...(isWindows ? {} : { reusePort: true }),\n      },\n      () => {\n        log(`✅ Backend server running on port ${port}`);\n        log(`🌐 API available at: http://localhost:${port}/api`);\n        log(`🔍 Health check: http://localhost:${port}/api/health`);\n        log(`🐛 Debug routes: http://localhost:${port}/api/debug/routes`);\n      }\n    );\n    \n    // Handle graceful shutdown\n    const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];\n    signals.forEach((signal) => {\n      process.on(signal, () => {\n        log(`Received ${signal}, gracefully shutting down...`);\n        \n        // Set a timeout for forceful shutdown if graceful shutdown takes too long\n        const forcefulShutdownTimeout = setTimeout(() => {\n          log('Forceful shutdown timeout reached, exiting immediately!');\n          process.exit(1);\n        }, 30000); // 30 seconds timeout\n        \n        // Attempt graceful shutdown\n        httpServer.close(() => {\n          log('HTTP server closed successfully.');\n          clearTimeout(forcefulShutdownTimeout);\n          process.exit(0);\n        });\n      });\n    });\n  } catch (error: any) {\n    console.error(\"❌ Failed to start server:\", error);\n    console.error(\"Error details:\", error.message);\n    console.error(\"Stack trace:\", error.stack);\n    process.exit(1);\n  }\n})();\n", "path": "server/index.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["4a7b779a-619d-4317-b090-05fa59e9c27f", {"value": {"selectedCode": "", "prefix": "// Load environment variables FIRST, before any other imports\nimport path from \"path\";\nimport { fileURLToPath } from \"url\";\nimport { config as dotenvConfig } from \"dotenv\";\n\n// Get current file's directory in ES modules\nconst __filename = fileURLToPath(import.meta.url);\nconst __dirname = path.dirname(__filename);\n\n// Load environment variables immediately in development\nif (process.env.NODE_ENV !== \"production\") {\n  const envPath = path.resolve(__dirname, \"../.env\");\n  dotenvConfig({ path: envPath });\n}\n\nimport express, { type Request, Response, NextFunction } from \"express\";\nimport cors from \"cors\";\nimport { Readable } from \"stream\"; // Import Readable\n\n// Extend RequestInit to include Node.js specific duplex option\ninterface NodeRequestInit extends RequestInit {\n  duplex?: \"half\" | \"full\";\n}\n\n// Route imports moved to after environment loading\n\nconst app = express();\n\n// Disable Express's ETag headers for API routes to avoid 304 responses\napp.disable(\"etag\");\n\n// Ensure dynamic API responses are never cached\n", "suffix": "app.use((req, res, next) => {\n  if (req.path.startsWith(\"/api\")) {\n    res.setHeader(\"Cache-Control\", \"no-store\");\n  }\n  next();\n});\n\n// CORS configuration for frontend-backend separation\napp.use(\n  cors({\n    // Allow requests from the frontend server\n    origin: process.env.NODE_ENV === \"production\"\n      ? [\"http://localhost:3000\", process.env.FRONTEND_URL].filter(Boolean)\n      : [\"http://localhost:3000\", \"http://localhost:5173\", process.env.FRONTEND_URL].filter(Boolean),\n    credentials: true,\n    methods: [\"GET\", \"POST\", \"PUT\", \"DELETE\", \"OPTIONS\"],\n    allowedHeaders: [\"Content-Type\", \"Authorization\"],\n  })\n);\n\napp.use(express.json({ limit: \"10mb\" }));\napp.use(express.urlencoded({ extended: false }));\n\n// Debug middleware to log API requests\napp.use((req, res, next) => {\n  if (req.path.startsWith(\"/api\")) {\n    console.log(\n      `${req.method} ${req.path} - Body keys: ${Object.keys(req.body).join(\n        \", \"\n      )}`\n    );\n  }\n  next();\n});\n\n// Serve static files in production - moved to async function below\n\napp.use((req, res, next) => {\n  const start = Date.now();\n  const path = req.path;\n  let capturedJsonResponse: Record<string, any> | undefined = undefined;\n\n  const originalResJson = res.json;\n  res.json = function (bodyJson, ...args) {\n    capturedJsonResponse = bodyJson;\n    return originalResJson.apply(res, [bodyJson, ...args]);\n  };\n\n  res.on(\"finish\", () => {\n    const duration = Date.now() - start;\n    if (path.startsWith(\"/api\")) {\n      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;\n      if (capturedJsonResponse) {\n        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;\n      }\n\n      if (logLine.length > 80) {\n        logLine = logLine.slice(0, 79) + \"…\";\n      }\n\n      console.log(logLine);\n    }\n  });\n\n  next();\n});\n\n// Route mounting moved to async function after environment loading\n\n// Helper function to convert Node.js stream to Web stream if needed\nasync function nodeStreamToWebStream(\n  nodeStream: NodeJS.ReadableStream\n): Promise<ReadableStream<Uint8Array>> {\n  return new ReadableStream({\n    start(controller) {\n      nodeStream.on(\"data\", (chunk) =>\n        controller.enqueue(new Uint8Array(chunk))\n      );\n      nodeStream.on(\"end\", () => controller.close());\n      nodeStream.on(\"error\", (err) => controller.error(err));\n    },\n  });\n}\n\n// Adapter middleware for Hono to Express\nconst honoAdapter = (honoApp: any, basePath: string = \"\") => {\n  return async (\n    req: express.Request,\n    res: express.Response,\n    next: express.NextFunction\n  ) => {\n    try {\n      const hasBody = req.method !== \"GET\" && req.method !== \"HEAD\" && req.body;\n      const requestBody = hasBody\n        ? req.body instanceof Readable\n          ? await nodeStreamToWebStream(req.body)\n          : new ReadableStream({\n              start: (controller) => {\n                controller.enqueue(Buffer.from(JSON.stringify(req.body)));\n                controller.close();\n              },\n            })\n        : undefined;\n\n      // Strip the base path from the URL for Hono\n      let honoPath = req.url;\n      if (basePath && req.url.startsWith(basePath)) {\n        honoPath = req.url.substring(basePath.length) || \"/\";\n      }\n\n      const webRequest = new Request(\n        `${req.protocol}://${req.get(\"host\")}${honoPath}`,\n        {\n          method: req.method,\n          headers: new Headers(req.headers as HeadersInit),\n          body: requestBody,\n          // Required for Node.js 18+ when sending a body\n          duplex: hasBody ? \"half\" : undefined,\n        } as NodeRequestInit\n      );\n\n      console.log(`[honoAdapter] Forwarding ${webRequest.method} ${webRequest.url} to Hono app. Path for Hono: ${honoPath}`);\n      const webResponse = await honoApp.fetch(webRequest);\n\n      res.status(webResponse.status);\n      webResponse.headers.forEach((value: string, key: string) => {\n        res.setHeader(key, value);\n      });\n\n      if (webResponse.body) {\n        const reader = webResponse.body.getReader();\n        const forwardStream = async () => {\n          while (true) {\n            const { done, value } = await reader.read();\n            if (done) break;\n            res.write(value);\n          }\n          res.end();\n        };\n        await forwardStream();\n      } else {\n        res.end();\n      }\n    } catch (error) {\n      next(error);\n    }\n  };\n};\n\n// All route mounting moved to async function after environment loading\n\n(async () => {\n  console.log(\"🚀 Starting server initialization...\");\n\n  // Environment variables already loaded at the top of the file\n\n  // Now import routes after environment variables are loaded\n  const { registerRoutes } = await import(\"./routes\");\n  const { log } = await import(\"./vite\");\n  const aiRoutes = (await import(\"./routes/aiRoutes\")).default;\n  const quizRoutes = (await import(\"./routes/quizRoutes\")).default;\n  const documentRoutes = (await import(\"./routes/documentRoutes\")).default;\n  const flashcardDeckRoutes = (await import(\"./routes/flashcardDeckRoutes\")).default;\n  const flashcardRoutes = (await import(\"./routes/flashcardRoutes\")).default;\n  const flashcardSetRoutes = (await import(\"./routes/flashcardSetRoutes\")).default;\n  const testRoutes = (await import(\"./routes/testRoutes\")).default;\n  const healthRoutes = (await import(\"./routes/healthRoutes\")).default;\n  const credentialsRoutes = (await import(\"./routes/credentialsRoutes\")).default;\n  const authRoutes = (await import(\"./routes/authRoutes\")).default;\n\n  // Mount routes after imports\n  console.log(\"🔧 Mounting AI routes at /api\");\n  app.use(\"/api\", aiRoutes);\n  console.log(\"🔧 Mounting flashcard deck routes at /api/decks\");\n  app.use(\"/api/decks\", flashcardDeckRoutes);\n  console.log(\"🔧 Mounting test routes at /api\");\n  app.use(\"/api\", testRoutes);\n  console.log(\"🔧 Mounting health check routes at /api/health\");\n  app.use(\"/api/health\", healthRoutes);\n  console.log(\"🔧 Mounting credentials routes at /api/credentials\");\n  app.use(\"/api/credentials\", credentialsRoutes);\n  console.log(\"🔧 Mounting auth routes at /api/auth\");\n  app.use(\"/api/auth\", authRoutes);\n\n  // Mount Express routes directly\n  console.log(\"🔧 Mounting document routes at /api/documents\");\n  app.use(\"/api/documents\", documentRoutes);\n\n  // Mount Hono routes with the adapter\n  console.log(\"🔧 Mounting quiz routes at /api/quizzes\");\n  app.use(\"/api/quizzes\", honoAdapter(quizRoutes, \"/api/quizzes\"));\n\n  console.log(\"🔧 Mounting flashcard set routes at /api/flashcard-sets\");\n  app.use(\"/api/flashcard-sets\", honoAdapter(flashcardSetRoutes, \"/api/flashcard-sets\"));\n\n  console.log(\"🔧 Mounting individual flashcard routes at /api/flashcards\");\n  app.use(\"/api/flashcards\", honoAdapter(flashcardRoutes, \"/api/flashcards\"));\n\n  try {\n\n    const server = await registerRoutes(app);\n    console.log(\"✅ Routes registered successfully\");\n\n    app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {\n      console.error(\"Unhandled error in Main Express App:\", err);\n      const status = err.status || err.statusCode || 500;\n      const responseBody: { error: string; message: string; stack?: string } = {\n        error: \"An unexpected server error occurred.\",\n        message: err.message || \"Internal Server Error\",\n      };\n      // Optionally include stack in development\n      if (app.get(\"env\") === \"development\" && err.stack) {\n        responseBody.stack = err.stack;\n      }\n      res.status(status).json(responseBody);\n      // Error is logged and JSON response sent. Do not call _next(err) or throw err to prevent Vite's HTML error.\n    });\n\n\n\n    // Get port from environment variable or use default\n    // In production (Replit deployment), use port 80\n    // In development, use port 5000\n    const port = parseInt(process.env.PORT || \"5000\", 10);\n    const isWindows = process.platform === \"win32\";\n\n    log(`Starting server in ${process.env.NODE_ENV || 'development'} mode on port ${port}`);\n\n    // Log environment info for debugging\n    log(`Environment: NODE_ENV=${process.env.NODE_ENV}, PORT=${process.env.PORT}`);\n    log(`API Base URL will be: ${process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:5000/api'}`);\n\n    const httpServer = server.listen(\n      {\n        port,\n        host: \"0.0.0.0\",\n        ...(isWindows ? {} : { reusePort: true }),\n      },\n      () => {\n        log(`✅ Backend server running on port ${port}`);\n        log(`🌐 API available at: http://localhost:${port}/api`);\n        log(`🔍 Health check: http://localhost:${port}/api/health`);\n        log(`🐛 Debug routes: http://localhost:${port}/api/debug/routes`);\n      }\n    );\n    \n    // Handle graceful shutdown\n    const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];\n    signals.forEach((signal) => {\n      process.on(signal, () => {\n        log(`Received ${signal}, gracefully shutting down...`);\n        \n        // Set a timeout for forceful shutdown if graceful shutdown takes too long\n        const forcefulShutdownTimeout = setTimeout(() => {\n          log('Forceful shutdown timeout reached, exiting immediately!');\n          process.exit(1);\n        }, 30000); // 30 seconds timeout\n        \n        // Attempt graceful shutdown\n        httpServer.close(() => {\n          log('HTTP server closed successfully.');\n          clearTimeout(forcefulShutdownTimeout);\n          process.exit(0);\n        });\n      });\n    });\n  } catch (error: any) {\n    console.error(\"❌ Failed to start server:\", error);\n    console.error(\"Error details:\", error.message);\n    console.error(\"Stack trace:\", error.stack);\n    process.exit(1);\n  }\n})();\n", "path": "server/index.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["4c05f936-745b-47ea-8e8f-92e630c2fe88", {"value": {"selectedCode": "", "prefix": "// Load environment variables FIRST, before any other imports\nimport path from \"path\";\nimport { fileURLToPath } from \"url\";\nimport { config as dotenvConfig } from \"dotenv\";\n\n// Get current file's directory in ES modules\nconst __filename = fileURLToPath(import.meta.url);\nconst __dirname = path.dirname(__filename);\n\n// Load environment variables immediately in development\nif (process.env.NODE_ENV !== \"production\") {\n  const envPath = path.resolve(__dirname, \"../.env\");\n  dotenvConfig({ path: envPath });\n}\n\nimport express, { type Request, Response, NextFunction } from \"express\";\nimport cors from \"cors\";\nimport { Readable } from \"stream\"; // Import Readable\n\n// Extend RequestInit to include Node.js specific duplex option\ninterface NodeRequestInit extends RequestInit {\n", "suffix": "  duplex?: \"half\" | \"full\";\n}\n\n// Route imports moved to after environment loading\n\nconst app = express();\n\n// Disable Express's ETag headers for API routes to avoid 304 responses\napp.disable(\"etag\");\n\n// Ensure dynamic API responses are never cached\napp.use((req, res, next) => {\n  if (req.path.startsWith(\"/api\")) {\n    res.setHeader(\"Cache-Control\", \"no-store\");\n  }\n  next();\n});\n\n// CORS configuration for frontend-backend separation\napp.use(\n  cors({\n    // Allow requests from the frontend server\n    origin: process.env.NODE_ENV === \"production\"\n      ? [\"http://localhost:3000\", process.env.FRONTEND_URL].filter(Boolean)\n      : [\"http://localhost:3000\", \"http://localhost:5173\", process.env.FRONTEND_URL].filter(Boolean),\n    credentials: true,\n    methods: [\"GET\", \"POST\", \"PUT\", \"DELETE\", \"OPTIONS\"],\n    allowedHeaders: [\"Content-Type\", \"Authorization\"],\n  })\n);\n\napp.use(express.json({ limit: \"10mb\" }));\napp.use(express.urlencoded({ extended: false }));\n\n// Debug middleware to log API requests\napp.use((req, res, next) => {\n  if (req.path.startsWith(\"/api\")) {\n    console.log(\n      `${req.method} ${req.path} - Body keys: ${Object.keys(req.body).join(\n        \", \"\n      )}`\n    );\n  }\n  next();\n});\n\n// Serve static files in production - moved to async function below\n\napp.use((req, res, next) => {\n  const start = Date.now();\n  const path = req.path;\n  let capturedJsonResponse: Record<string, any> | undefined = undefined;\n\n  const originalResJson = res.json;\n  res.json = function (bodyJson, ...args) {\n    capturedJsonResponse = bodyJson;\n    return originalResJson.apply(res, [bodyJson, ...args]);\n  };\n\n  res.on(\"finish\", () => {\n    const duration = Date.now() - start;\n    if (path.startsWith(\"/api\")) {\n      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;\n      if (capturedJsonResponse) {\n        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;\n      }\n\n      if (logLine.length > 80) {\n        logLine = logLine.slice(0, 79) + \"…\";\n      }\n\n      console.log(logLine);\n    }\n  });\n\n  next();\n});\n\n// Route mounting moved to async function after environment loading\n\n// Helper function to convert Node.js stream to Web stream if needed\nasync function nodeStreamToWebStream(\n  nodeStream: NodeJS.ReadableStream\n): Promise<ReadableStream<Uint8Array>> {\n  return new ReadableStream({\n    start(controller) {\n      nodeStream.on(\"data\", (chunk) =>\n        controller.enqueue(new Uint8Array(chunk))\n      );\n      nodeStream.on(\"end\", () => controller.close());\n      nodeStream.on(\"error\", (err) => controller.error(err));\n    },\n  });\n}\n\n// Adapter middleware for Hono to Express\nconst honoAdapter = (honoApp: any, basePath: string = \"\") => {\n  return async (\n    req: express.Request,\n    res: express.Response,\n    next: express.NextFunction\n  ) => {\n    try {\n      const hasBody = req.method !== \"GET\" && req.method !== \"HEAD\" && req.body;\n      const requestBody = hasBody\n        ? req.body instanceof Readable\n          ? await nodeStreamToWebStream(req.body)\n          : new ReadableStream({\n              start: (controller) => {\n                controller.enqueue(Buffer.from(JSON.stringify(req.body)));\n                controller.close();\n              },\n            })\n        : undefined;\n\n      // Strip the base path from the URL for Hono\n      let honoPath = req.url;\n      if (basePath && req.url.startsWith(basePath)) {\n        honoPath = req.url.substring(basePath.length) || \"/\";\n      }\n\n      const webRequest = new Request(\n        `${req.protocol}://${req.get(\"host\")}${honoPath}`,\n        {\n          method: req.method,\n          headers: new Headers(req.headers as HeadersInit),\n          body: requestBody,\n          // Required for Node.js 18+ when sending a body\n          duplex: hasBody ? \"half\" : undefined,\n        } as NodeRequestInit\n      );\n\n      console.log(`[honoAdapter] Forwarding ${webRequest.method} ${webRequest.url} to Hono app. Path for Hono: ${honoPath}`);\n      const webResponse = await honoApp.fetch(webRequest);\n\n      res.status(webResponse.status);\n      webResponse.headers.forEach((value: string, key: string) => {\n        res.setHeader(key, value);\n      });\n\n      if (webResponse.body) {\n        const reader = webResponse.body.getReader();\n        const forwardStream = async () => {\n          while (true) {\n            const { done, value } = await reader.read();\n            if (done) break;\n            res.write(value);\n          }\n          res.end();\n        };\n        await forwardStream();\n      } else {\n        res.end();\n      }\n    } catch (error) {\n      next(error);\n    }\n  };\n};\n\n// All route mounting moved to async function after environment loading\n\n(async () => {\n  console.log(\"🚀 Starting server initialization...\");\n\n  // Environment variables already loaded at the top of the file\n\n  // Now import routes after environment variables are loaded\n  const { registerRoutes } = await import(\"./routes\");\n  const { log } = await import(\"./vite\");\n  const aiRoutes = (await import(\"./routes/aiRoutes\")).default;\n  const quizRoutes = (await import(\"./routes/quizRoutes\")).default;\n  const documentRoutes = (await import(\"./routes/documentRoutes\")).default;\n  const flashcardDeckRoutes = (await import(\"./routes/flashcardDeckRoutes\")).default;\n  const flashcardRoutes = (await import(\"./routes/flashcardRoutes\")).default;\n  const flashcardSetRoutes = (await import(\"./routes/flashcardSetRoutes\")).default;\n  const testRoutes = (await import(\"./routes/testRoutes\")).default;\n  const healthRoutes = (await import(\"./routes/healthRoutes\")).default;\n  const credentialsRoutes = (await import(\"./routes/credentialsRoutes\")).default;\n  const authRoutes = (await import(\"./routes/authRoutes\")).default;\n\n  // Mount routes after imports\n  console.log(\"🔧 Mounting AI routes at /api\");\n  app.use(\"/api\", aiRoutes);\n  console.log(\"🔧 Mounting flashcard deck routes at /api/decks\");\n  app.use(\"/api/decks\", flashcardDeckRoutes);\n  console.log(\"🔧 Mounting test routes at /api\");\n  app.use(\"/api\", testRoutes);\n  console.log(\"🔧 Mounting health check routes at /api/health\");\n  app.use(\"/api/health\", healthRoutes);\n  console.log(\"🔧 Mounting credentials routes at /api/credentials\");\n  app.use(\"/api/credentials\", credentialsRoutes);\n  console.log(\"🔧 Mounting auth routes at /api/auth\");\n  app.use(\"/api/auth\", authRoutes);\n\n  // Mount Express routes directly\n  console.log(\"🔧 Mounting document routes at /api/documents\");\n  app.use(\"/api/documents\", documentRoutes);\n\n  // Mount Hono routes with the adapter\n  console.log(\"🔧 Mounting quiz routes at /api/quizzes\");\n  app.use(\"/api/quizzes\", honoAdapter(quizRoutes, \"/api/quizzes\"));\n\n  console.log(\"🔧 Mounting flashcard set routes at /api/flashcard-sets\");\n  app.use(\"/api/flashcard-sets\", honoAdapter(flashcardSetRoutes, \"/api/flashcard-sets\"));\n\n  console.log(\"🔧 Mounting individual flashcard routes at /api/flashcards\");\n  app.use(\"/api/flashcards\", honoAdapter(flashcardRoutes, \"/api/flashcards\"));\n\n  try {\n\n    const server = await registerRoutes(app);\n    console.log(\"✅ Routes registered successfully\");\n\n    app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {\n      console.error(\"Unhandled error in Main Express App:\", err);\n      const status = err.status || err.statusCode || 500;\n      const responseBody: { error: string; message: string; stack?: string } = {\n        error: \"An unexpected server error occurred.\",\n        message: err.message || \"Internal Server Error\",\n      };\n      // Optionally include stack in development\n      if (app.get(\"env\") === \"development\" && err.stack) {\n        responseBody.stack = err.stack;\n      }\n      res.status(status).json(responseBody);\n      // Error is logged and JSON response sent. Do not call _next(err) or throw err to prevent Vite's HTML error.\n    });\n\n\n\n    // Get port from environment variable or use default\n    // In production (Replit deployment), use port 80\n    // In development, use port 5000\n    const port = parseInt(process.env.PORT || \"5000\", 10);\n    const isWindows = process.platform === \"win32\";\n\n    log(`Starting server in ${process.env.NODE_ENV || 'development'} mode on port ${port}`);\n\n    // Log environment info for debugging\n    log(`Environment: NODE_ENV=${process.env.NODE_ENV}, PORT=${process.env.PORT}`);\n    log(`API Base URL will be: ${process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:5000/api'}`);\n\n    const httpServer = server.listen(\n      {\n        port,\n        host: \"0.0.0.0\",\n        ...(isWindows ? {} : { reusePort: true }),\n      },\n      () => {\n        log(`✅ Backend server running on port ${port}`);\n        log(`🌐 API available at: http://localhost:${port}/api`);\n        log(`🔍 Health check: http://localhost:${port}/api/health`);\n        log(`🐛 Debug routes: http://localhost:${port}/api/debug/routes`);\n      }\n    );\n    \n    // Handle graceful shutdown\n    const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];\n    signals.forEach((signal) => {\n      process.on(signal, () => {\n        log(`Received ${signal}, gracefully shutting down...`);\n        \n        // Set a timeout for forceful shutdown if graceful shutdown takes too long\n        const forcefulShutdownTimeout = setTimeout(() => {\n          log('Forceful shutdown timeout reached, exiting immediately!');\n          process.exit(1);\n        }, 30000); // 30 seconds timeout\n        \n        // Attempt graceful shutdown\n        httpServer.close(() => {\n          log('HTTP server closed successfully.');\n          clearTimeout(forcefulShutdownTimeout);\n          process.exit(0);\n        });\n      });\n    });\n  } catch (error: any) {\n    console.error(\"❌ Failed to start server:\", error);\n    console.error(\"Error details:\", error.message);\n    console.error(\"Stack trace:\", error.stack);\n    process.exit(1);\n  }\n})();\n", "path": "server/index.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}]]